import os
import json
import time

from pymongo import MongoClient


# Kafka Config
kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
group_id = os.getenv("KAFKA_GROUP_ID", "2")

# MongoDB Config
mongo_uri = os.getenv("MONGO_URI", "mongodb+srv://staging:<EMAIL>/aggregator?retryWrites=true&w=majority")
mongo_db_name = os.getenv("MONGO_DATABASE", "onpremserver")

# Mongo Connection
mongo_client = MongoClient(mongo_uri)
mongo_db = mongo_client[mongo_db_name]


def get_waiterapp_topics(expected_min=14, retries=20, wait_sec=5):
    print("🔍 Discovering waiterapp.* topics from Kafka...")
    admin = AdminClient({'bootstrap.servers': kafka_bootstrap_servers})

    waiterapp_topics = []  # Initialize to avoid undefined variable

    for attempt in range(1, retries + 1):
        print(f"⏳ Attempt {attempt} of {retries}...")
        try:
            # Increase timeout and add request_timeout for better reliability
            topic_metadata = admin.list_topics(timeout=60, request_timeout=45)

            if topic_metadata is None or not hasattr(topic_metadata, 'topics'):
                print(f"⚠ No topic metadata received on attempt {attempt}")
                continue

            all_topics = list(topic_metadata.topics.keys())
            print(f"🧾 All Kafka Topics (Attempt {attempt}): {sorted(all_topics)}")

            # More flexible topic matching - check both cases and log what we're looking for
            waiterapp_topics = []
            for topic in all_topics:
                if topic.lower().startswith("waiterapp."):
                    waiterapp_topics.append(topic)

            print(f"📋 waiterapp.* topics found ({len(waiterapp_topics)}): {sorted(waiterapp_topics)}")

            # Always return all found topics, regardless of expected_min
            if waiterapp_topics:
                if len(waiterapp_topics) >= expected_min:
                    print(f"✅ Found {len(waiterapp_topics)} topics (>= expected {expected_min}): {sorted(waiterapp_topics)}")
                else:
                    print(f"⚠ Found {len(waiterapp_topics)} topics (< expected {expected_min}): {sorted(waiterapp_topics)}")
                    print(f"🔄 Continuing to retry for more topics...")

                # If this is the last attempt or we have enough topics, return what we found
                if attempt == retries or len(waiterapp_topics) >= expected_min:
                    return sorted(waiterapp_topics)  # Return sorted for consistency

        except KafkaException as ke:
            print(f"❌ Kafka error on attempt {attempt}: {ke}")
        except Exception as e:
            print(f"❌ General error on attempt {attempt}: {e}")

        if attempt < retries:  # Don't sleep after the last attempt
            print(f"⏳ Waiting {wait_sec}s before next retry...")
            time.sleep(wait_sec)

    print(f"⚠ Returning {len(waiterapp_topics)} available waiterapp.* topics after {retries} retries: {sorted(waiterapp_topics)}")
    return sorted(waiterapp_topics)


def consume_messages(topics):
    consumer = Consumer({
        'bootstrap.servers': kafka_bootstrap_servers,
        'group.id': group_id,
        'auto.offset.reset': 'earliest'
    })

    consumer.subscribe(topics)
    print(f"📡 Subscribed to topics: {topics}")

    try:
        while True:
            msg = consumer.poll(1.0)
            if msg is None:
                continue

            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    continue
                else:
                    raise KafkaException(msg.error())

            try:
                topic = msg.topic()
                collection_name = topic.split('.')[-1].lower()
                mongo_collection = mongo_db[collection_name]

                decoded = msg.value().decode('utf-8')
                raw_data = json.loads(decoded)

                payload = raw_data.get("payload")
                if not payload:
                    print(f"[{collection_name}] ⚠ Skipped: No payload key")
                    continue

                final_data = payload.get("after")
                if not final_data or not isinstance(final_data, dict):
                    print(f"[{collection_name}] ⚠ Skipped: payload.after is empty or not a dict")
                    continue

                # Insert/Upsert document
                if "_id" in final_data:
                    mongo_collection.replace_one({"_id": final_data["_id"]}, final_data, upsert=True)
                else:
                    mongo_collection.insert_one(final_data)

                print(f"[{collection_name}] ✅ Document inserted: {final_data}")

            except Exception as e:
                print(f"❌ Error in topic [{msg.topic()}]: {e}")

    except KeyboardInterrupt:
        print("🛑 Consumer manually stopped.")
    finally:
        consumer.close()
        mongo_client.close()
        print("🔒 Closed Kafka and Mongo connections.")


if __name__ == "__main__":
    # First, let's see what topics are actually available
    print("🚀 Starting Kafka Consumer...")

    # Get all waiterapp topics - set expected_min to 0 to get ALL available topics
    topics = get_waiterapp_topics(expected_min=0, retries=10, wait_sec=3)

    if topics:
        print(f"🎯 Final topic list to consume: {topics}")
        consume_messages(topics)
    else:
        print("❗ No waiterapp.* topics found after retries.")
        print("💡 Try checking your Kafka cluster status and topic names.")