import os
import json
import time
from confluent_kafka import Consumer, KafkaException, KafkaError
from pymongo import MongoClient
from confluent_kafka.admin import AdminClient

# Kafka Config
kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
group_id = os.getenv("KAFKA_GROUP_ID", "2")

# MongoDB Config
mongo_uri = os.getenv("MONGO_URI", "mongodb+srv://staging:<EMAIL>/aggregator?retryWrites=true&w=majority")
mongo_db_name = os.getenv("MONGO_DATABASE", "onpremserver")

# Mongo Connection
mongo_client = MongoClient(mongo_uri)
mongo_db = mongo_client[mongo_db_name]


def get_waiterapp_topics(expected_min=14, retries=20, wait_sec=5):
    print("🔍 Discovering waiterapp.* topics from Kafka...")
    admin = AdminClient({'bootstrap.servers': kafka_bootstrap_servers})

    for attempt in range(1, retries + 1):
        print(f"⏳ Attempt {attempt} of {retries}...")
        try:
            topic_metadata = admin.list_topics(timeout=30)
            all_topics = list(topic_metadata.topics.keys())

            print(f"🧾 All Kafka Topics (Attempt {attempt}): {all_topics}")

            waiterapp_topics = [t for t in all_topics if t.lower().startswith("waiterapp.")]
            print(f"📋 waiterapp.* topics found: {len(waiterapp_topics)}")

            if len(waiterapp_topics) >= expected_min:
                print(f"✅ Topics ready: {waiterapp_topics}")
                return waiterapp_topics

        except Exception as e:
            print(f"❌ Error fetching topics: {e}")

        print(f"⏳ Waiting {wait_sec}s before next retry...")
        time.sleep(wait_sec)

    print("⚠ Returning available waiterapp.* topics after retries.")
    return waiterapp_topics


def consume_messages(topics):
    consumer = Consumer({
        'bootstrap.servers': kafka_bootstrap_servers,
        'group.id': group_id,
        'auto.offset.reset': 'earliest'
    })

    consumer.subscribe(topics)
    print(f"📡 Subscribed to topics: {topics}")

    try:
        while True:
            msg = consumer.poll(1.0)
            if msg is None:
                continue

            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    continue
                else:
                    raise KafkaException(msg.error())

            try:
                topic = msg.topic()
                collection_name = topic.split('.')[-1].lower()
                mongo_collection = mongo_db[collection_name]

                decoded = msg.value().decode('utf-8')
                raw_data = json.loads(decoded)

                payload = raw_data.get("payload")
                if not payload:
                    print(f"[{collection_name}] ⚠ Skipped: No payload key")
                    continue

                final_data = payload.get("after")
                if not final_data or not isinstance(final_data, dict):
                    print(f"[{collection_name}] ⚠ Skipped: payload.after is empty or not a dict")
                    continue

                # Insert/Upsert document
                if "_id" in final_data:
                    mongo_collection.replace_one({"_id": final_data["_id"]}, final_data, upsert=True)
                else:
                    mongo_collection.insert_one(final_data)

                print(f"[{collection_name}] ✅ Document inserted: {final_data}")

            except Exception as e:
                print(f"❌ Error in topic [{msg.topic()}]: {e}")

    except KeyboardInterrupt:
        print("🛑 Consumer manually stopped.")
    finally:
        consumer.close()
        mongo_client.close()
        print("🔒 Closed Kafka and Mongo connections.")


if _name_ == "_main_":
    topics = get_waiterapp_topics(expected_min=15)  # Update this as per your topic count
    if topics:
        consume_messages(topics)
    else:
        print("❗ No waiterapp.* topics found after retries.")