import os
import json
import time
from confluent_kafka import Consumer, KafkaException, KafkaError
from pymongo import MongoClient
from confluent_kafka.admin import AdminClient

# Kafka Config
kafka_bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
group_id = os.getenv("KAFKA_GROUP_ID", "2")

# MongoDB Config
mongo_uri = os.getenv("MONGO_URI", "mongodb+srv://staging:<EMAIL>/aggregator?retryWrites=true&w=majority")
mongo_db_name = os.getenv("MONGO_DATABASE", "onpremserver")

# Mongo Connection
mongo_client = MongoClient(mongo_uri)
mongo_db = mongo_client[mongo_db_name]


def get_waiterapp_topics(expected_min=19, retries=25, wait_sec=3):
    print("🔍 Discovering waiterapp.* topics from Kafka...")
    print(f"🎯 Target: Find all {expected_min} waiterapp collections")

    # Aggressive admin client configurations with maximum timeouts for 19 collections
    admin_configs = [
        {
            'bootstrap.servers': kafka_bootstrap_servers,
            'request.timeout.ms': 300000,  # 5 minutes
            'socket.timeout.ms': 300000,   # 5 minutes
            'metadata.max.age.ms': 300000, # 5 minutes
            'api.version.request': True
        },
        {
            'bootstrap.servers': kafka_bootstrap_servers,
            'request.timeout.ms': 180000,  # 3 minutes
            'socket.timeout.ms': 180000,   # 3 minutes
            'metadata.max.age.ms': 180000
        },
        {
            'bootstrap.servers': kafka_bootstrap_servers,
            'request.timeout.ms': 120000,  # 2 minutes
            'socket.timeout.ms': 120000    # 2 minutes
        },
        {
            'bootstrap.servers': kafka_bootstrap_servers,
            'request.timeout.ms': 60000,   # 1 minute
            'socket.timeout.ms': 60000
        }
    ]

    all_found_topics = set()  # Use set to avoid duplicates across attempts

    for config_idx, admin_config in enumerate(admin_configs):
        print(f"🔧 Trying admin config {config_idx + 1}/{len(admin_configs)}")
        admin = AdminClient(admin_config)

        for attempt in range(1, retries + 1):
            print(f"⏳ Config {config_idx + 1} - Attempt {attempt} of {retries}...")
            try:
                # Aggressive timeout strategy for 19 collections
                if attempt <= 5:
                    timeouts = [300, 240, 180]  # 5min, 4min, 3min for first 5 attempts
                elif attempt <= 10:
                    timeouts = [180, 120, 90]   # 3min, 2min, 1.5min for next 5 attempts
                elif attempt <= 15:
                    timeouts = [120, 90, 60]    # 2min, 1.5min, 1min for next 5 attempts
                else:
                    timeouts = [90, 60, 30]     # 1.5min, 1min, 30s for final attempts

                for timeout in timeouts:
                    try:
                        topic_metadata = admin.list_topics(timeout=timeout)

                        if topic_metadata is None or not hasattr(topic_metadata, 'topics'):
                            print(f"⚠ No topic metadata received (timeout: {timeout}s)")
                            continue

                        all_topics = list(topic_metadata.topics.keys())
                        print(f"🧾 Found {len(all_topics)} total topics (timeout: {timeout}s)")

                        # Find waiterapp topics with multiple patterns
                        current_waiterapp_topics = set()
                        patterns = ["waiterapp.", "waiterApp.", "WaiterApp.", "WAITERAPP."]

                        for topic in all_topics:
                            for pattern in patterns:
                                if topic.startswith(pattern):
                                    current_waiterapp_topics.add(topic)
                                    break

                        if current_waiterapp_topics:
                            all_found_topics.update(current_waiterapp_topics)
                            progress = len(all_found_topics)
                            print(f"📋 waiterapp.* topics in this attempt ({len(current_waiterapp_topics)}): {sorted(current_waiterapp_topics)}")
                            print(f"🎯 Progress: {progress}/19 collections found so far: {sorted(all_found_topics)}")

                            # Check if we have all 19 collections
                            if progress >= 19:
                                print(f"🎉 SUCCESS! Found all {progress} collections!")
                                return sorted(list(all_found_topics))

                            # If we found topics, break from timeout loop
                            break

                    except Exception as timeout_error:
                        print(f"⚠ Timeout {timeout}s failed: {timeout_error}")
                        continue

                # If we found topics in this attempt, we can be more confident
                if current_waiterapp_topics:
                    break

            except KafkaException as ke:
                print(f"❌ Kafka error (Config {config_idx + 1}, Attempt {attempt}): {ke}")
            except Exception as e:
                print(f"❌ General error (Config {config_idx + 1}, Attempt {attempt}): {e}")

            if attempt < retries:
                print(f"⏳ Waiting {wait_sec}s before next retry...")
                time.sleep(wait_sec)

        # If we found topics with this config, we might not need to try other configs
        if all_found_topics:
            print(f"✅ Found topics with config {config_idx + 1}, total: {len(all_found_topics)}")
            break

    final_topics = sorted(list(all_found_topics))
    print(f"🏁 FINAL RESULT: Found {len(final_topics)} waiterapp.* topics: {final_topics}")
    return final_topics


def consume_messages(topics):
    consumer = Consumer({
        'bootstrap.servers': kafka_bootstrap_servers,
        'group.id': group_id,
        'auto.offset.reset': 'earliest'
    })

    consumer.subscribe(topics)
    print(f"📡 Subscribed to topics: {topics}")

    try:
        while True:
            msg = consumer.poll(1.0)
            if msg is None:
                continue

            if msg.error():
                if msg.error().code() == KafkaError._PARTITION_EOF:
                    continue
                else:
                    raise KafkaException(msg.error())

            try:
                topic = msg.topic()
                collection_name = topic.split('.')[-1].lower()
                mongo_collection = mongo_db[collection_name]

                decoded = msg.value().decode('utf-8')
                raw_data = json.loads(decoded)

                # Extract only the payload data (not the entire schema wrapper)
                payload = raw_data.get("payload")
                if not payload:
                    print(f"[{collection_name}] ⚠ Skipped: No payload key")
                    continue

                # Use the entire payload as the data to insert (not just payload.after)
                final_data = payload
                if not final_data or not isinstance(final_data, dict):
                    print(f"[{collection_name}] ⚠ Skipped: payload is empty or not a dict")
                    continue

                # Insert/Upsert document using only payload data
                if "_id" in final_data:
                    mongo_collection.replace_one({"_id": final_data["_id"]}, final_data, upsert=True)
                    print(f"[{collection_name}] ✅ Document upserted with _id: {final_data.get('_id')}")
                else:
                    result = mongo_collection.insert_one(final_data)
                    print(f"[{collection_name}] ✅ Document inserted with new _id: {result.inserted_id}")

                # Log only a sample of the data to avoid cluttering logs
                sample_keys = list(final_data.keys())[:5]  # Show first 5 keys
                print(f"[{collection_name}] 📄 Payload keys: {sample_keys}{'...' if len(final_data) > 5 else ''}")

            except Exception as e:
                print(f"❌ Error in topic [{msg.topic()}]: {e}")

    except KeyboardInterrupt:
        print("🛑 Consumer manually stopped.")
    finally:
        consumer.close()
        mongo_client.close()
        print("🔒 Closed Kafka and Mongo connections.")


if __name__ == "__main__":
    # First, let's see what topics are actually available
    print("🚀 Starting Kafka Consumer...")

    # Get all waiterapp topics - set expected_min to 0 to get ALL available topics
    topics = get_waiterapp_topics(expected_min=19, retries=25, wait_sec=3)

    if topics:
        print(f"� SUCCESS: Will consume from {len(topics)} topics: {topics}")
        print("📦 Note: Only 'payload' data will be stored (not the full schema wrapper)")
        consume_messages(topics)
    else:
        print("❗ No waiterapp.* topics found after retries.")
        print("💡 Try checking your Kafka cluster status and topic names.")